# BRAND_KIT

# zPass Brand Kit & Design System

## 🎨 Brand Identity

### Project Name

**zPass** - Decentralized Reputation System | DeFi Analytics Dashboard

### Brand Description

A comprehensive analytics dashboard for zScore reputation system showcasing <PERSON><PERSON><PERSON> use cases with professional, high-quality design featuring animations, and gradients that enhance credibility.

### Design Philosophy

- **Professional & Credible**: Apple/Stripe-inspired modern aesthetics
- **Dark Mode First**: Sophisticated dark theme with gradient accents
- **Mobile Responsive**: Mobile-first design approach
- **Performance Focused**: Optimized animations and interactions
- **Accessibility Conscious**: High contrast ratios and semantic HTML

---

## 🎯 Logo & Visual Identity

### Logo

- **Primary Logo**: `/public/logo copy.svg`
- **Favicon**: `/public/favicon.svg`
- **Logo Color**: `#5e23c7` (Purple)
- **Logo Usage**: Top-left positioning in navigation, not centered
- **Logo Dimensions**: 32x32px standard, scalable SVG format

### Brand Colors

### Primary Palette

```css
/* Main Background Colors */--background: #0A0D1F           /* Deep navy base */--surface: #1A1E35              /* Card/component backgrounds */--surface-light: #252A47        /* Lighter surface variant */--surface-hover: #2D3454        /* Hover states *//* Primary Brand Colors */--primary: #6366F1              /* Indigo - main brand color */--primary-light: #818CF8        /* Light indigo variant */--primary-dark: #4F46E5         /* Dark indigo variant */--secondary: #10B981            /* Emerald - success/positive */--secondary-light: #34D399      /* Light emerald variant *//* Accent Colors */--accent: #8B5CF6               /* Violet - secondary brand */--accent-light: #A78BFA         /* Light violet variant */--accent-cyan: #06B6D4          /* Cyan accent */--accent-pink: #EC4899          /* Pink accent */--accent-orange: #F59E0B        /* Orange/amber accent */
```

### Status Colors

```css
--success: #10B981              /* Green for success states */--warning: #F59E0B              /* Amber for warnings */--danger: #EF4444               /* Red for errors/danger */--info: #3B82F6                 /* Blue for information */
```

### Text Colors

```css
--text-primary: #F8FAFC         /* Primary text - high contrast */--text-secondary: #CBD5E1       /* Secondary text - medium contrast */--text-muted: #94A3B8           /* Muted text - low contrast */--text-accent: #A78BFA          /* Accent text color */
```

### Border & UI Colors

```css
--border-color: #374151         /* Default borders */--border-light: #4B5563         /* Lighter borders */--border-accent: #6366F1        /* Accent borders */
```

### Chart Colors (Data Visualization)

```css
--chart-1: #6366F1              /* Indigo */--chart-2: #10B981              /* Emerald */--chart-3: #F59E0B              /* Amber */--chart-4: #EF4444              /* Red */--chart-5: #8B5CF6              /* Violet */--chart-6: #06B6D4              /* Cyan */--chart-7: #EC4899              /* Pink */--chart-8: #84CC16              /* Lime */
```

---

## 📝 Typography

### Font Family

```css
font-family: 'Inter', 'system-ui', 'sans-serif'
```

### Font Weights

- **Light**: 300
- **Regular**: 400
- **Medium**: 500
- **Semibold**: 600
- **Bold**: 700

### Typography Scale

```css
/* Headlines */h1: text-4xl sm:text-5xl font-bold     /* 36px/48px+ */h2: text-2xl font-bold                 /* 24px */h3: text-xl font-semibold              /* 20px */h4: text-lg font-medium                /* 18px *//* Body Text */body-large: text-lg                    /* 18px */body: text-base                        /* 16px */body-small: text-sm                    /* 14px */caption: text-xs                       /* 12px */
```

### Font Features

```css
font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'
font-variant-numeric: tabular-nums
-webkit-font-smoothing: antialiased
-moz-osx-font-smoothing: grayscale
text-rendering: optimizeLegibility
line-height: 1.6
```

---

### Gradient Backgrounds

```css
/* Primary Gradient */.gradient-bg {
  background: linear-gradient(135deg, #6366F1, #8B5CF6);}
/* Body Background */.body-gradient {
  background: linear-gradient(135deg, #0A0D1F 0%, #0F1629 50%, #1A1E35 100%);  background-attachment: fixed;}
/* Card Gradients */.card-gradient {
  background: linear-gradient(135deg, #1A1E35, #252A47);}
```

### Shadow System

```css
/* Card Shadows */.shadow-card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3),
              0 2px 4px -1px rgba(0, 0, 0, 0.2);}
.shadow-card-hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4),
              0 4px 6px -2px rgba(0, 0, 0, 0.3);}
/* Glow Effects */.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);}
.glow-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);}
.glow-emerald {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);}
```

---

## 📱 Responsive Design

### Breakpoints

```css
xs: '475px'
sm: '640px'
md: '768px'
lg: '1024px'
xl: '1280px'
2xl: '1536px'
```

### Mobile-First Approach

- Start with mobile design
- Progressive enhancement for larger screens
- Touch-friendly interactive elements
- Collapsible navigation for mobile

---

## 🧩 Component Standards

### Button Styles

```css
/* Primary Button */.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);}
.btn-primary:hover {
  transform: translateY(-2px);  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);}
/* Secondary Button */.btn-secondary {
  border: 1px solid rgba(255, 255, 255, 0.2);  background: transparent;  color: #CBD5E1;}
.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.05);  border-color: rgba(255, 255, 255, 0.3);  color: #F8FAFC;}
```

### Card Components

```css
.card {
  background: rgba(255, 255, 255, 0.05);  backdrop-filter: blur(20px);  border: 1px solid rgba(255, 255, 255, 0.1);  border-radius: 16px;  padding: 32px;  transition: all 0.3s ease;}
.card:hover {
  background: rgba(255, 255, 255, 0.08);  border-color: rgba(255, 255, 255, 0.2);  transform: translateY(-4px);}
```

### Navigation Patterns

- Logo positioned in top-left (not center)
- Menu items centered in navigation bar
- Mobile hamburger menu with slide-out sidebar
- Sticky navigation with backdrop blur
- URL routing integration

---

## 🎯 Usage Guidelines

### Do’s

✅ Use professional, high-quality designs
✅ Maintain consistent spacing and typography
✅ Follow mobile-first responsive design
✅ Use semantic HTML and proper accessibility
✅ Implement smooth, professional animations
✅ Maintain high contrast for readability

### Don’ts

❌ Don’t Use childish or unprofessional designs
❌ Don’t Implement overly bright or garish colors
❌ Don’t Center-align logos in navigation
❌ Don’t Ignore mobile responsiveness
❌ Don’t Use poor contrast ratios
❌ Don’t Implement jarring or excessive animations
❌ Don’t Use outdated design patterns

---

## 📊 Data Visualization

### Chart Color Palette

Use the predefined chart colors in sequence for consistent data representation:
1. `#6366F1` (Indigo) - Primary data
2. `#10B981` (Emerald) - Positive/success data
3. `#F59E0B` (Amber) - Warning/attention data
4. `#EF4444` (Red) - Error/danger data
5. `#8B5CF6` (Violet) - Secondary data
6. `#06B6D4` (Cyan) - Tertiary data
7. `#EC4899` (Pink) - Quaternary data
8. `#84CC16` (Lime) - Additional data