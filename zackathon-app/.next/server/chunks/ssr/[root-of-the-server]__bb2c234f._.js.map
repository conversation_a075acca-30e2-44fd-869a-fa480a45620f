{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function truncateAddress(address: string, chars = 4): string {\n  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`\n}\n\nexport function calculatePoints(apiCalls: number, apiType: string, userEngagement: number): number {\n  const basePoints = apiCalls * 10\n  \n  // API type multipliers\n  const typeMultipliers: Record<string, number> = {\n    'wallet-analytics': 2.0,\n    'defi-score': 1.8,\n    'reputation': 1.5,\n    'transaction-history': 1.2,\n    'basic': 1.0,\n  }\n  \n  const typeMultiplier = typeMultipliers[apiType] || 1.0\n  const engagementBonus = userEngagement * 5\n  \n  return Math.floor(basePoints * typeMultiplier + engagementBonus)\n}\n\nexport function getRankSuffix(rank: number): string {\n  if (rank % 100 >= 11 && rank % 100 <= 13) {\n    return 'th'\n  }\n  switch (rank % 10) {\n    case 1: return 'st'\n    case 2: return 'nd'\n    case 3: return 'rd'\n    default: return 'th'\n  }\n}\n\nexport function validateApiKey(apiKey: string): boolean {\n  // Basic API key validation - should be 32+ characters alphanumeric\n  return /^[a-zA-Z0-9]{32,}$/.test(apiKey)\n}\n\nexport function generateShareText(appName: string, rank: number, points: number): string {\n  return `🚀 Just built ${appName} using @zPass APIs and ranked #${rank} in #zackathon Season 1! \n  \n💎 ${points} points earned building on wallet-level intelligence\n🔥 Join the hackathon: zackathon.dev\n  \n#Web3 #BuildOnzPass #Hackathon`\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,gBAAgB,OAAe,EAAE,QAAQ,CAAC;IACxD,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACpE;AAEO,SAAS,gBAAgB,QAAgB,EAAE,OAAe,EAAE,cAAsB;IACvF,MAAM,aAAa,WAAW;IAE9B,uBAAuB;IACvB,MAAM,kBAA0C;QAC9C,oBAAoB;QACpB,cAAc;QACd,cAAc;QACd,uBAAuB;QACvB,SAAS;IACX;IAEA,MAAM,iBAAiB,eAAe,CAAC,QAAQ,IAAI;IACnD,MAAM,kBAAkB,iBAAiB;IAEzC,OAAO,KAAK,KAAK,CAAC,aAAa,iBAAiB;AAClD;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI,OAAO,OAAO,MAAM,OAAO,OAAO,IAAI;QACxC,OAAO;IACT;IACA,OAAQ,OAAO;QACb,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf;YAAS,OAAO;IAClB;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,mEAAmE;IACnE,OAAO,qBAAqB,IAAI,CAAC;AACnC;AAEO,SAAS,kBAAkB,OAAe,EAAE,IAAY,EAAE,MAAc;IAC7E,OAAO,CAAC,cAAc,EAAE,QAAQ,+BAA+B,EAAE,KAAK;;GAErE,EAAE,OAAO;;;8BAGkB,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/LoginModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { X, Mail, Lock, User, Eye, EyeOff } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface LoginModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport default function LoginModal({ isOpen, onClose }: LoginModalProps) {\n  const [isLogin, setIsLogin] = useState(true)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [name, setName] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp, signInWithGoogle } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      let result\n      if (isLogin) {\n        result = await signIn(email, password)\n      } else {\n        result = await signUp(email, password, name)\n      }\n\n      if (result.error) {\n        setError(result.error.message)\n      } else {\n        onClose()\n        // Reset form\n        setEmail('')\n        setPassword('')\n        setName('')\n      }\n    } catch (err) {\n      setError('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleGoogleSignIn = async () => {\n    setLoading(true)\n    setError('')\n\n    try {\n      const { error } = await signInWithGoogle()\n      if (error) {\n        setError(error.message)\n      }\n    } catch (err) {\n      setError('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetForm = () => {\n    setEmail('')\n    setPassword('')\n    setName('')\n    setError('')\n    setShowPassword(false)\n  }\n\n  const switchMode = () => {\n    setIsLogin(!isLogin)\n    resetForm()\n  }\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={onClose}\n            className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n          />\n\n          {/* Modal */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.95, y: 20 }}\n            className=\"relative w-full max-w-md\"\n          >\n            <div className=\"card p-8\">\n              {/* Header */}\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-2xl font-bold text-text-primary\">\n                  {isLogin ? 'Welcome Back' : 'Join zackathon'}\n                </h2>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                >\n                  <X className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              {/* Google Sign In */}\n              <button\n                onClick={handleGoogleSignIn}\n                disabled={loading}\n                className=\"w-full flex items-center justify-center space-x-3 p-3 border border-border-color rounded-lg text-text-primary hover:bg-surface-hover transition-colors duration-200 mb-6 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n                <span>Continue with Google</span>\n              </button>\n\n              {/* Divider */}\n              <div className=\"relative mb-6\">\n                <div className=\"absolute inset-0 flex items-center\">\n                  <div className=\"w-full border-t border-border-color\"></div>\n                </div>\n                <div className=\"relative flex justify-center text-sm\">\n                  <span className=\"px-2 bg-surface text-text-muted\">or</span>\n                </div>\n              </div>\n\n              {/* Form */}\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                {!isLogin && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-text-secondary mb-2\">\n                      Name\n                    </label>\n                    <div className=\"relative\">\n                      <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-muted\" />\n                      <input\n                        type=\"text\"\n                        value={name}\n                        onChange={(e) => setName(e.target.value)}\n                        className=\"w-full pl-10 pr-4 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                        placeholder=\"Your name\"\n                        required={!isLogin}\n                      />\n                    </div>\n                  </div>\n                )}\n\n                <div>\n                  <label className=\"block text-sm font-medium text-text-secondary mb-2\">\n                    Email\n                  </label>\n                  <div className=\"relative\">\n                    <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-muted\" />\n                    <input\n                      type=\"email\"\n                      value={email}\n                      onChange={(e) => setEmail(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                      placeholder=\"<EMAIL>\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-text-secondary mb-2\">\n                    Password\n                  </label>\n                  <div className=\"relative\">\n                    <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-muted\" />\n                    <input\n                      type={showPassword ? 'text' : 'password'}\n                      value={password}\n                      onChange={(e) => setPassword(e.target.value)}\n                      className=\"w-full pl-10 pr-12 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                      placeholder=\"••••••••\"\n                      required\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPassword(!showPassword)}\n                      className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-text-muted hover:text-text-primary\"\n                    >\n                      {showPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n                    </button>\n                  </div>\n                </div>\n\n                {error && (\n                  <div className=\"p-3 bg-danger/10 border border-danger/20 rounded-lg\">\n                    <p className=\"text-danger text-sm\">{error}</p>\n                  </div>\n                )}\n\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {loading ? 'Loading...' : isLogin ? 'Sign In' : 'Create Account'}\n                </button>\n              </form>\n\n              {/* Switch Mode */}\n              <div className=\"mt-6 text-center\">\n                <p className=\"text-text-muted\">\n                  {isLogin ? \"Don't have an account?\" : 'Already have an account?'}\n                  <button\n                    onClick={switchMode}\n                    className=\"ml-2 text-primary hover:text-primary-light font-medium\"\n                  >\n                    {isLogin ? 'Sign up' : 'Sign in'}\n                  </button>\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAYe,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAmB;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEnD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI;YACJ,IAAI,SAAS;gBACX,SAAS,MAAM,OAAO,OAAO;YAC/B,OAAO;gBACL,SAAS,MAAM,OAAO,OAAO,UAAU;YACzC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK,CAAC,OAAO;YAC/B,OAAO;gBACL;gBACA,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,QAAQ;YACV;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;YACxB,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,YAAY;QACZ,QAAQ;QACR,SAAS;QACT,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,WAAW,CAAC;QACZ;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAM,GAAG;oBAAG;oBACvC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,UAAU,iBAAiB;;;;;;kDAE9B,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,SAAQ;;0DAC/B,8OAAC;gDACC,MAAK;gDACL,GAAE;;;;;;0DAEJ,8OAAC;gDACC,MAAK;gDACL,GAAE;;;;;;0DAEJ,8OAAC;gDACC,MAAK;gDACL,GAAE;;;;;;0DAEJ,8OAAC;gDACC,MAAK;gDACL,GAAE;;;;;;;;;;;;kDAGN,8OAAC;kDAAK;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;0CAKtD,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;oCACrC,CAAC,yBACA,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAqD;;;;;;0DAGtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wDACvC,WAAU;wDACV,aAAY;wDACZ,UAAU,CAAC;;;;;;;;;;;;;;;;;;kDAMnB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAqD;;;;;;0DAGtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAqD;;;;;;0DAGtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDACC,MAAM,eAAe,SAAS;wDAC9B,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;kEAEV,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,gBAAgB,CAAC;wDAChC,WAAU;kEAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAAe,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oCAKrE,uBACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAuB;;;;;;;;;;;kDAIxC,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,UAAU,eAAe,UAAU,YAAY;;;;;;;;;;;;0CAKpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCACV,UAAU,2BAA2B;sDACtC,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAET,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C", "debugId": null}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Menu, X, Trophy, Code, BookOpen, Users, LogIn, LogOut, User } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginModal from './LoginModal'\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [showLoginModal, setShowLoginModal] = useState(false)\n  const { user, signOut, loading } = useAuth()\n\n  const navItems = [\n    { name: 'Leaderboard', href: '#leaderboard', icon: Trophy },\n    { name: 'How to Participate', href: '#participate', icon: Code },\n    { name: 'About zPass', href: '#about', icon: BookOpen },\n    { name: 'Dashboard', href: '/dashboard', icon: Users },\n  ]\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border-color\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">z</span>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-text-primary font-bold text-lg leading-none\">zackathon</span>\n              <span className=\"text-text-muted text-xs leading-none\">Season 1</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 text-text-secondary hover:text-text-primary transition-colors duration-200\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button / User Menu */}\n          <div className=\"hidden md:block\">\n            {user ? (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/dashboard\"\n                  className=\"btn-secondary flex items-center space-x-2\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Dashboard</span>\n                </Link>\n                <button\n                  onClick={() => signOut()}\n                  className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                  title=\"Sign out\"\n                >\n                  <LogOut className=\"w-5 h-5\" />\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <button\n                  onClick={() => setShowLoginModal(true)}\n                  className=\"btn-secondary flex items-center space-x-2\"\n                >\n                  <LogIn className=\"w-4 h-4\" />\n                  <span>Sign In</span>\n                </button>\n                <Link\n                  href=\"/dashboard\"\n                  className=\"btn-primary\"\n                >\n                  Start Building\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n          >\n            {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div\n        className={cn(\n          \"md:hidden transition-all duration-300 ease-in-out\",\n          isOpen\n            ? \"max-h-96 opacity-100\"\n            : \"max-h-0 opacity-0 overflow-hidden\"\n        )}\n      >\n        <div className=\"px-4 py-4 space-y-4 bg-surface border-t border-border-color\">\n          {navItems.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              onClick={() => setIsOpen(false)}\n              className=\"flex items-center space-x-3 text-text-secondary hover:text-text-primary transition-colors duration-200 py-2\"\n            >\n              <item.icon className=\"w-5 h-5\" />\n              <span>{item.name}</span>\n            </Link>\n          ))}\n          <div className=\"pt-4 border-t border-border-color\">\n            <Link\n              href=\"/dashboard\"\n              onClick={() => setIsOpen(false)}\n              className=\"btn-primary w-full text-center block\"\n            >\n              Start Building\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Login Modal */}\n      <LoginModal\n        isOpen={showLoginModal}\n        onClose={() => setShowLoginModal(false)}\n      />\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzC,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;YAAgB,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC1D;YAAE,MAAM;YAAsB,MAAM;YAAgB,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC/D;YAAE,MAAM;YAAe,MAAM;YAAU,MAAM,8MAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,oMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAmD;;;;;;sDACnE,8OAAC;4CAAK,WAAU;sDAAuC;;;;;;;;;;;;;;;;;;sCAK3D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;sCACZ,qBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM;wCACf,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;qDAItB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5D,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA,SACI,yBACA;0BAGN,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,UAAU;gCACzB,WAAU;;kDAEV,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BANX,KAAK,IAAI;;;;;sCASlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,gIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,kBAAkB;;;;;;;;;;;;AAIzC", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport Navigation from '@/components/Navigation'\nimport DashboardStats from '@/components/dashboard/DashboardStats'\nimport ApiKeySection from '@/components/dashboard/ApiKeySection'\nimport ApplicationForm from '@/components/dashboard/ApplicationForm'\nimport UserApplications from '@/components/dashboard/UserApplications'\nimport SocialShare from '@/components/dashboard/SocialShare'\nimport ReferralSection from '@/components/dashboard/ReferralSection'\nimport LoginModal from '@/components/LoginModal'\n\nexport default function Dashboard() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n  const [showLoginModal, setShowLoginModal] = useState(false)\n\n  useEffect(() => {\n    if (!loading && !user) {\n      setShowLoginModal(true)\n    }\n  }, [user, loading])\n\n  const handleLoginClose = () => {\n    if (!user) {\n      router.push('/')\n    }\n    setShowLoginModal(false)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-body\">\n      <Navigation />\n      \n      {user ? (\n        <main className=\"pt-20 pb-12\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            {/* Header */}\n            <div className=\"mb-8\">\n              <h1 className=\"text-3xl font-bold text-text-primary mb-2\">\n                Welcome back, {user.user_metadata?.name || user.email?.split('@')[0]}!\n              </h1>\n              <p className=\"text-text-secondary\">\n                Track your progress, manage your applications, and climb the leaderboard.\n              </p>\n            </div>\n\n            {/* Dashboard Grid */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n              {/* Left Column */}\n              <div className=\"lg:col-span-2 space-y-8\">\n                <DashboardStats />\n                <ApplicationForm />\n                <UserApplications />\n              </div>\n\n              {/* Right Column */}\n              <div className=\"space-y-8\">\n                <ApiKeySection />\n                <SocialShare />\n                <ReferralSection />\n              </div>\n            </div>\n          </div>\n        </main>\n      ) : (\n        <div className=\"min-h-screen flex items-center justify-center pt-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl font-bold text-text-primary mb-4\">\n              Access Dashboard\n            </h1>\n            <p className=\"text-text-secondary mb-8\">\n              Sign in to access your zackathon dashboard and start building.\n            </p>\n            <button\n              onClick={() => setShowLoginModal(true)}\n              className=\"btn-primary\"\n            >\n              Sign In to Continue\n            </button>\n          </div>\n        </div>\n      )}\n\n      <LoginModal \n        isOpen={showLoginModal} \n        onClose={handleLoginClose} \n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA;AAZA;;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAM;KAAQ;IAElB,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;QACd;QACA,kBAAkB;IACpB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;YAEV,qBACC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA4C;wCACzC,KAAK,aAAa,EAAE,QAAQ,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE;wCAAC;;;;;;;8CAEvE,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;;;;sDACD,8OAAC;;;;;sDACD,8OAAC;;;;;;;;;;;8CAIH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;;;;sDACD,8OAAC;;;;;sDACD,8OAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAMT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAGxC,8OAAC;4BACC,SAAS,IAAM,kBAAkB;4BACjC,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOP,8OAAC,gIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}