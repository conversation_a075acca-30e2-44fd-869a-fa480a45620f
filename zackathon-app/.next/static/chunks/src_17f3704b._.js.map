{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport function createSupabaseClient() {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;AACxD,MAAM,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;AAE1D,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAE3C,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { createSupabaseClient } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, name?: string) => Promise<{ error: any }>\n  signInWithGoogle: () => Promise<{ error: any }>\n  signOut: () => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setSession(session)\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setSession(session)\n        setUser(session?.user ?? null)\n        setLoading(false)\n\n        // Create user profile if it doesn't exist\n        if (event === 'SIGNED_IN' && session?.user) {\n          const { error } = await supabase\n            .from('users')\n            .upsert({\n              id: session.user.id,\n              email: session.user.email,\n              name: session.user.user_metadata?.name || session.user.user_metadata?.full_name,\n              avatar_url: session.user.user_metadata?.avatar_url,\n              updated_at: new Date().toISOString(),\n            }, {\n              onConflict: 'id'\n            })\n\n          if (error) {\n            console.error('Error creating user profile:', error)\n          }\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase])\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string, name?: string) => {\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          name: name,\n        },\n      },\n    })\n    return { error }\n  }\n\n  const signInWithGoogle = async () => {\n    const { error } = await supabase.auth.signInWithOAuth({\n      provider: 'google',\n      options: {\n        redirectTo: `${window.location.origin}/dashboard`,\n      },\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    return { error }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signIn,\n    signUp,\n    signInWithGoogle,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAC5D,WAAW;oBACX,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,WAAW;oBACX,QAAQ,SAAS,QAAQ;oBACzB,WAAW;oBAEX,0CAA0C;oBAC1C,IAAI,UAAU,eAAe,SAAS,MAAM;wBAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;4BACN,IAAI,QAAQ,IAAI,CAAC,EAAE;4BACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,MAAM,QAAQ,IAAI,CAAC,aAAa,EAAE,QAAQ,QAAQ,IAAI,CAAC,aAAa,EAAE;4BACtE,YAAY,QAAQ,IAAI,CAAC,aAAa,EAAE;4BACxC,YAAY,IAAI,OAAO,WAAW;wBACpC,GAAG;4BACD,YAAY;wBACd;wBAEF,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,gCAAgC;wBAChD;oBACF;gBACF;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG;QAAC;KAAS;IAEb,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,MAAM;gBACR;YACF;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,mBAAmB;QACvB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;YACpD,UAAU;YACV,SAAS;gBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;YACnD;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAnGgB;KAAA;AAqGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}