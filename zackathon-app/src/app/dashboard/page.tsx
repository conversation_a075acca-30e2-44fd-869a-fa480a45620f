'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Navigation from '@/components/Navigation'
import LoginModal from '@/components/LoginModal'

export default function Dashboard() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [showLoginModal, setShowLoginModal] = useState(false)

  useEffect(() => {
    if (!loading && !user) {
      setShowLoginModal(true)
    }
  }, [user, loading])

  const handleLoginClose = () => {
    if (!user) {
      router.push('/')
    }
    setShowLoginModal(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-body">
      <Navigation />
      
      {user ? (
        <main className="pt-20 pb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-text-primary mb-2">
                Welcome back, {user.user_metadata?.name || user.email?.split('@')[0]}!
              </h1>
              <p className="text-text-secondary">
                Track your progress, manage your applications, and climb the leaderboard.
              </p>
            </div>

            {/* Dashboard Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column */}
              <div className="lg:col-span-2 space-y-8">
                <DashboardStats />
                <ApplicationForm />
                <UserApplications />
              </div>

              {/* Right Column */}
              <div className="space-y-8">
                <ApiKeySection />
                <SocialShare />
                <ReferralSection />
              </div>
            </div>
          </div>
        </main>
      ) : (
        <div className="min-h-screen flex items-center justify-center pt-20">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-text-primary mb-4">
              Access Dashboard
            </h1>
            <p className="text-text-secondary mb-8">
              Sign in to access your zackathon dashboard and start building.
            </p>
            <button
              onClick={() => setShowLoginModal(true)}
              className="btn-primary"
            >
              Sign In to Continue
            </button>
          </div>
        </div>
      )}

      <LoginModal 
        isOpen={showLoginModal} 
        onClose={handleLoginClose} 
      />
    </div>
  )
}
