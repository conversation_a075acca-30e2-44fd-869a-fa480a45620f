'use client'

import Link from 'next/link'
import { Twitter, Github, Discord, Mail, ExternalLink } from 'lucide-react'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    hackathon: [
      { name: 'Leaderboard', href: '#leaderboard' },
      { name: 'How to Participate', href: '#participate' },
      { name: 'FAQ', href: '#faq' },
      { name: 'Dashboard', href: '/dashboard' },
    ],
    zpass: [
      { name: 'About zPass', href: '#about' },
      { name: 'Documentation', href: 'https://docs.zpass.dev', external: true },
      { name: 'API Reference', href: 'https://api.zpass.dev', external: true },
      { name: 'Status Page', href: 'https://status.zpass.dev', external: true },
    ],
    community: [
      { name: 'Discord', href: 'https://discord.gg/zpass', external: true },
      { name: 'Twitter', href: 'https://twitter.com/zpass_dev', external: true },
      { name: 'GitHub', href: 'https://github.com/zpass-dev', external: true },
      { name: 'Blog', href: 'https://blog.zpass.dev', external: true },
    ],
    legal: [
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Code of Conduct', href: '/code-of-conduct' },
      { name: 'Contact', href: 'mailto:<EMAIL>', external: true },
    ],
  }

  const socialLinks = [
    { name: 'Twitter', href: 'https://twitter.com/zpass_dev', icon: Twitter },
    { name: 'Discord', href: 'https://discord.gg/zpass', icon: Discord },
    { name: 'GitHub', href: 'https://github.com/zpass-dev', icon: Github },
    { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail },
  ]

  return (
    <footer className="bg-surface border-t border-border-color">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">z</span>
              </div>
              <div className="flex flex-col">
                <span className="text-text-primary font-bold text-lg leading-none">zackathon</span>
                <span className="text-text-muted text-xs leading-none">Season 1</span>
              </div>
            </Link>
            <p className="text-text-secondary text-sm mb-4">
              Build the future of Web3 with zPass wallet intelligence APIs. Join Season 1 and compete for $50K in prizes.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200"
                  aria-label={social.name}
                >
                  <social.icon className="w-5 h-5" />
                </a>
              ))}
            </div>
          </div>

          {/* Hackathon Links */}
          <div>
            <h3 className="text-text-primary font-semibold mb-4">Hackathon</h3>
            <ul className="space-y-3">
              {footerLinks.hackathon.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* zPass Links */}
          <div>
            <h3 className="text-text-primary font-semibold mb-4">zPass</h3>
            <ul className="space-y-3">
              {footerLinks.zpass.map((link) => (
                <li key={link.name}>
                  {link.external ? (
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm flex items-center space-x-1"
                    >
                      <span>{link.name}</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  ) : (
                    <Link
                      href={link.href}
                      className="text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>

          {/* Community Links */}
          <div>
            <h3 className="text-text-primary font-semibold mb-4">Community</h3>
            <ul className="space-y-3">
              {footerLinks.community.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm flex items-center space-x-1"
                  >
                    <span>{link.name}</span>
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h3 className="text-text-primary font-semibold mb-4">Legal</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  {link.external ? (
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </a>
                  ) : (
                    <Link
                      href={link.href}
                      className="text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-border-color pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-text-muted text-sm mb-4 md:mb-0">
              © {currentYear} zPass. All rights reserved. Built for Web3 builders.
            </div>
            <div className="flex items-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                <span className="text-text-muted">Season 1 Live</span>
              </div>
              <div className="text-text-muted">
                Made with ❤️ for the Web3 community
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
